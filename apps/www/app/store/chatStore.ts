import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import type {
  ChatMessage,
  Thread,
  ThreadTree,
  ThreadMessage,
} from '~/types/chat';

export interface ChatState {
  threads: Thread[];
  currentThreadId: string | null;
  messagesByThread: Record<string, ChatMessage[]>;
  threadTrees: Record<string, ThreadTree>; // Cache for complete thread trees
  streamingResponses: Record<string, ChatMessage>;
  threadsStatus: 'idle' | 'loading' | 'success' | 'error';
  inputValue: string;
  unreadThreads: string[];
}

interface ChatActions {
  setThreads: (threads: Thread[]) => void;
  addThread: (thread: Thread) => void;
  updateThread: (threadId: string, title: string) => void;
  setThreadsStatus: (status: ChatState['threadsStatus']) => void;
  setCurrentThreadId: (threadId: string | null) => void;
  setMessagesForThread: (threadId: string, messages: ChatMessage[]) => void;
  addMessageToThread: (threadId: string, message: ChatMessage) => void;
  replaceMessageInThread: (
    threadId: string,
    messageIdToReplace: string,
    newMessage: ChatMessage,
  ) => void;
  removeMessageFromThread: (threadId: string, messageId: string) => void;
  startStreaming: (threadId: string, message: ChatMessage) => void;
  updateStreamingContent: (
    threadId: string,
    contentChunk: string,
    debugInfo?: ChatMessage['debug'],
  ) => void;
  finalizeStreaming: (threadId: string, finalMessage: ChatMessage) => void;
  abortStreaming: (threadId: string) => void;
  getThreadById: (id: string | null) => Thread | undefined;
  setInputValue: (value: string) => void;
  addUnreadThread: (threadId: string) => void;
  removeUnreadThread: (threadId: string) => void;

  // Thread tree actions for optimized navigation
  setThreadTree: (threadId: string, threadTree: ThreadTree) => void;
  getThreadTree: (threadId: string) => ThreadTree | undefined;
  navigateSiblingClientSide: (
    threadId: string,
    messageId: string,
    direction: 'next' | 'prev',
  ) => boolean;
  invalidateThreadTree: (threadId: string) => void;
}

type ChatStore = ChatState & ChatActions;

const THIRTY_MINUTES_IN_MS = 30 * 60 * 1000;

export const useChatStore = create<ChatStore>()(
  persist(
    (set, get) => ({
      // --- INITIAL STATE ---
      threads: [],
      currentThreadId: null,
      messagesByThread: {},
      threadTrees: {},
      streamingResponses: {},
      threadsStatus: 'idle',
      inputValue: '',
      unreadThreads: [],

      // --- ACTIONS ---
      setThreads: (threads) => set({ threads }),
      addThread: (thread) =>
        set((state) => ({ threads: [thread, ...state.threads] })),
      updateThread: (threadId, title) =>
        set((state) => ({
          threads: state.threads.map((t) =>
            t.id === threadId ? { ...t, title } : t,
          ),
        })),
      setThreadsStatus: (status) => set({ threadsStatus: status }),
      setCurrentThreadId: (threadId) => set({ currentThreadId: threadId }),
      setMessagesForThread: (threadId, messages) =>
        set((state) => ({
          messagesByThread: {
            ...state.messagesByThread,
            [threadId]: messages,
          },
        })),
      addMessageToThread: (threadId, message) =>
        set((state) => ({
          messagesByThread: {
            ...state.messagesByThread,
            [threadId]: [...(state.messagesByThread[threadId] || []), message],
          },
        })),
      replaceMessageInThread: (threadId, messageIdToReplace, newMessage) =>
        set((state) => ({
          messagesByThread: {
            ...state.messagesByThread,
            [threadId]: (state.messagesByThread[threadId] || []).map((m) =>
              m.id === messageIdToReplace ? newMessage : m,
            ),
          },
        })),
      removeMessageFromThread: (threadId, messageId) =>
        set((state) => ({
          messagesByThread: {
            ...state.messagesByThread,
            [threadId]: (state.messagesByThread[threadId] || []).filter(
              (m) => m.id !== messageId,
            ),
          },
        })),
      startStreaming: (threadId, message) => {
        set((state) => ({
          streamingResponses: {
            ...state.streamingResponses,
            [threadId]: message,
          },
        }));
      },
      updateStreamingContent: (threadId, contentChunk, debugInfo) => {
        set((state) => {
          const streamingMessage = state.streamingResponses[threadId];
          if (!streamingMessage) return {};
          return {
            streamingResponses: {
              ...state.streamingResponses,
              [threadId]: {
                ...streamingMessage,
                content: streamingMessage.content + contentChunk,
                ...(debugInfo && { debug: debugInfo }),
              },
            },
          };
        });
      },
      finalizeStreaming: (threadId, finalMessage) => {
        get().addMessageToThread(threadId, finalMessage);
        set((state) => {
          const { [threadId]: _, ...rest } = state.streamingResponses;
          return { streamingResponses: rest };
        });
      },
      abortStreaming: (threadId) => {
        set((state) => {
          const { [threadId]: _, ...rest } = state.streamingResponses;
          return { streamingResponses: rest };
        });
      },
      getThreadById: (id) => get().threads.find((t) => t.id === id),
      setInputValue: (value) => set({ inputValue: value }),
      addUnreadThread: (threadId) =>
        set((state) => ({
          // Use a Set to prevent duplicates
          unreadThreads: [...new Set([...state.unreadThreads, threadId])],
        })),
      removeUnreadThread: (threadId) =>
        set((state) => ({
          unreadThreads: state.unreadThreads.filter((id) => id !== threadId),
        })),

      // Thread tree actions for optimized navigation
      setThreadTree: (threadId, threadTree) =>
        set((state) => ({
          threadTrees: {
            ...state.threadTrees,
            [threadId]: threadTree,
          },
        })),

      getThreadTree: (threadId) => get().threadTrees[threadId],

      navigateSiblingClientSide: (threadId, messageId, direction) => {
        const threadTree = get().threadTrees[threadId];
        if (!threadTree) return false;

        const messageMap = new Map<string, ThreadMessage>();
        threadTree.messages.forEach((msg) => messageMap.set(msg.id, msg));

        const currentMessage = messageMap.get(messageId);
        if (!currentMessage || !currentMessage.parentId) return false;

        const parent = messageMap.get(currentMessage.parentId);
        if (!parent) return false;

        const siblings = parent.children.sort(
          (a, b) => a.siblingPosition - b.siblingPosition,
        );
        const currentIndex = siblings.findIndex((s) => s.id === messageId);
        if (currentIndex === -1) return false;

        const newIndex =
          direction === 'next' ? currentIndex + 1 : currentIndex - 1;
        if (newIndex < 0 || newIndex >= siblings.length) return false;

        const newSibling = siblings[newIndex];

        // This helper function reconstructs the rest of the path from a given starting node.
        // It simply follows the `nextMessageId` linked list.
        const buildPathFromNode = (
          startNode: ThreadMessage,
          pathPrefix: string[],
        ): string[] => {
          const newPath = [...pathPrefix];
          let current: ThreadMessage | undefined = startNode;
          while (current) {
            newPath.push(current.id);
            if (!current.nextMessageId) break;
            current = messageMap.get(current.nextMessageId);
          }
          return newPath;
        };

        const currentPathIndex = threadTree.currentPath.indexOf(messageId);
        let newPath: string[];

        if (currentPathIndex === -1) {
          // This case handles when the current message (e.g., a deleted one) isn't in the
          // main `currentPath`. We find its parent in the path and branch off from there.
          const parentIndex = threadTree.currentPath.indexOf(
            currentMessage.parentId,
          );
          if (parentIndex === -1) return false; // Parent must be in the path to branch from.

          const pathPrefix = threadTree.currentPath.slice(0, parentIndex + 1);
          newPath = buildPathFromNode(newSibling, pathPrefix);
        } else {
          // This is the standard case where we are branching off the current active path.
          const pathPrefix = threadTree.currentPath.slice(0, currentPathIndex);
          newPath = buildPathFromNode(newSibling, pathPrefix);
        }

        // --- Update State ---
        const updatedThreadTree = { ...threadTree, currentPath: newPath };
        const newMessages = newPath
          .map((id) => {
            const msg = messageMap.get(id);
            if (!msg) return null;
            // Convert ThreadMessageDto to ChatMessage for the UI
            return {
              id: msg.id,
              content: msg.content,
              role: msg.role,
              createdAt: msg.createdAt,
              model: msg.model,
              threadId: msg.threadId,
              parentId: msg.parentId,
              siblingCount: msg.siblingCount,
              siblingPosition: msg.siblingPosition,
              deletedAt: msg.deletedAt,
            } as ChatMessage;
          })
          .filter((msg): msg is ChatMessage => !!msg);

        set((state) => ({
          threadTrees: {
            ...state.threadTrees,
            [threadId]: updatedThreadTree,
          },
          messagesByThread: {
            ...state.messagesByThread,
            [threadId]: newMessages,
          },
        }));

        return true;
      },

      invalidateThreadTree: (threadId) =>
        set((state) => {
          const { [threadId]: removed, ...rest } = state.threadTrees;
          return { threadTrees: rest };
        }),
    }),
    {
      name: 'chat-storage', // storage name
      storage: createJSONStorage(() => sessionStorage), // use sessionStorage
      // On save, create an object with the value and a timestamp
      partialize: (state) => ({
        inputValue: {
          value: state.inputValue,
          timestamp: Date.now(),
        },
      }),
      // On load, check the timestamp before merging the state
      merge: (persistedState, currentState) => {
        const typedState = persistedState as {
          inputValue?: { value: string; timestamp: number };
        };

        if (typedState.inputValue) {
          const { timestamp } = typedState.inputValue;
          if (Date.now() - timestamp > THIRTY_MINUTES_IN_MS) {
            // Data is stale, ignore it and return the initial state.
            return currentState;
          }
        }

        // Data is fresh, so we manually merge the unwrapped value.
        return {
          ...currentState,
          ...(persistedState as object),
          inputValue: typedState.inputValue?.value || currentState.inputValue,
        };
      },
    },
  ),
);
