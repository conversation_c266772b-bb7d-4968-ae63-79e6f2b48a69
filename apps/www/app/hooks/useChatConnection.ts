import { useRef, useCallback, type Mouse<PERSON>vent } from 'react';
import { toast } from 'sonner';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import type { NavigateFunction } from 'react-router';
import { useChatStore } from '~/store/chatStore';
import type { ChatMessage, ThreadTree } from '~/types/chat';

// Custom error classes to control retry logic
class FatalError extends Error {}

/**
 * Manages multiple, independent SSE connections for chat responses.
 * Each connection is tied to a specific threadId.
 */
export function useChatConnection(navigate: NavigateFunction) {
  // Use a ref to store a map of active AbortControllers, keyed by threadId.
  const controllersRef = useRef<Record<string, AbortController>>({});

  const {
    startStreaming,
    updateStreamingContent,
    abortStreaming,
    getThreadById,
    addUnreadThread,
    setThreadTree,
    setMessagesForThread,
  } = useChatStore.getState();

  const startStreamingResponse = useCallback(
    async (parentId: string, threadId: string, isRegen = false) => {
      // If a controller for this thread already exists, abort the previous request.
      if (controllersRef.current[threadId]) {
        controllersRef.current[threadId].abort();
      }

      const ctrl = new AbortController();
      controllersRef.current[threadId] = ctrl;

      const streamUrl = isRegen
        ? `/api/chat/stream/${parentId}/regenerate`
        : `/api/chat/stream/${parentId}`;

      // Create a skeleton message and add it to the streaming state immediately.
      // This allows the UI to show a skeleton right away.
      const tempAiMessageId = `temp-ai-${Date.now()}`;
      startStreaming(threadId, {
        id: tempAiMessageId,
        content: '', // Start with empty content
        role: 'ASSISTANT',
        createdAt: new Date().toISOString(),
        threadId: threadId,
        parentId: parentId,
        siblingCount: 1,
        siblingPosition: 1,
      });

      await fetchEventSource(streamUrl, {
        signal: ctrl.signal,
        credentials: 'include',

        onopen: async (response) => {
          if (response.ok) return;
          if (
            response.status >= 400 &&
            response.status < 500 &&
            response.status !== 429
          ) {
            throw new FatalError(`Client Error: ${response.status}`);
          }
        },

        onmessage: (event) => {
          if (ctrl.signal.aborted || !event.data) return;
          const data = JSON.parse(event.data);

          if (data.isComplete && data.finalTree) {
            const finalTree = data.finalTree as ThreadTree;

            // 1. Update the thread tree cache in the store
            setThreadTree(threadId, finalTree);

            // 2. Reconstruct the message list from the new tree's current path
            const messageMap = new Map<string, any>();
            finalTree.messages.forEach((msg) => messageMap.set(msg.id, msg));
            const newMessages = finalTree.currentPath
              .map((id) => {
                const msg = messageMap.get(id);
                if (!msg) return null;
                return {
                  id: msg.id,
                  content: msg.content,
                  role: msg.role,
                  createdAt: msg.createdAt,
                  model: msg.model,
                  threadId: msg.threadId,
                  parentId: msg.parentId,
                  siblingCount: msg.siblingCount,
                  siblingPosition: msg.siblingPosition,
                  deletedAt: msg.deletedAt,
                } as ChatMessage;
              })
              .filter((msg): msg is ChatMessage => !!msg);

            // 3. Update the messages for the thread, making the UI consistent
            setMessagesForThread(threadId, newMessages);

            // 4. Abort the streaming state for this thread
            abortStreaming(threadId);

            // 5. Handle toast notifications for background threads
            const currentThreadId = useChatStore.getState().currentThreadId;
            if (threadId !== currentThreadId) {
              addUnreadThread(threadId); // Mark thread as unread
              const thread = getThreadById(threadId);
              const lastMessage = newMessages[newMessages.length - 1];
              toast.success(`New message in "${thread?.title || 'a chat'}"`, {
                description: lastMessage.content.substring(0, 100) + '...',
                action: {
                  label: 'View',
                  onClick: (e: MouseEvent<HTMLButtonElement>) => {
                    const url = `/c/${threadId}`;
                    if (e.ctrlKey || e.metaKey) {
                      window.open(url, '_blank', 'noopener,noreferrer');
                    } else {
                      navigate(url);
                    }
                  },
                },
              });
            }

            ctrl.abort(); // End this connection
            return;
          }

          const { content, debug } = data;
          updateStreamingContent(threadId, content, debug);
        },

        onclose: () => {
          // If the connection closes unexpectedly, clean up the streaming state for that thread.
          abortStreaming(threadId);
          delete controllersRef.current[threadId];
        },

        onerror: (err) => {
          abortStreaming(threadId); // Clean up on error
          delete controllersRef.current[threadId];

          if (err instanceof FatalError) {
            toast.error('A fatal error occurred.', {
              description: 'Please check your connection or try again later.',
            });
            throw err; // Stop retrying
          }
          toast.warning('Connection issue. Retrying...');
          return 2000; // Retry delay
        },
      });
    },
    [
      startStreaming,
      updateStreamingContent,
      abortStreaming,
      getThreadById,
      addUnreadThread,
      navigate,
      setThreadTree,
      setMessagesForThread,
    ],
  );

  return { startStreamingResponse };
}
