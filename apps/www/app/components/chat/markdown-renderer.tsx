// apps/www/app/components/chat/markdown-renderer.tsx
import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkMath from 'remark-math';
import remarkGfm from 'remark-gfm'; // Import GFM plugin for tables
import rehypeKatex from 'rehype-katex';
import rehypeRaw from 'rehype-raw';
import rehypeSanitize from 'rehype-sanitize';
import { useShikiHighlighter } from 'react-shiki/web';
import { toast } from 'sonner';
import { cn } from '~/lib/utils';

interface MarkdownRendererProps {
  content: string;
  isStreaming?: boolean;
}

// Safe sanitization schema - only allow table-related HTML elements
const tableSanitizeSchema = {
  tagNames: [
    // Standard markdown elements
    'p',
    'br',
    'strong',
    'em',
    'code',
    'pre',
    'a',
    'ul',
    'ol',
    'li',
    'h1',
    'h2',
    'h3',
    'h4',
    'h5',
    'h6',
    // Table elements
    'table',
    'thead',
    'tbody',
    'tfoot',
    'tr',
    'th',
    'td',
    'caption',
    'colgroup',
    'col',
  ],
  attributes: {
    // Allow basic attributes for tables
    table: ['class'],
    th: ['scope', 'colspan', 'rowspan', 'class', 'align'],
    td: ['colspan', 'rowspan', 'class', 'align'],
    a: ['href', 'title', 'target', 'rel'],
    '*': ['className'], // Allow className for styling
  },
  protocols: {
    href: ['http', 'https', 'mailto'],
  },
};

// Define a more accurate type for the props passed by `react-markdown` to custom components.
type CodeBlockProps = React.PropsWithChildren<{
  node?: any;
  className?: string;
  inline?: boolean;
  // Custom props passed down to give the component more context
  isGloballyStreaming?: boolean;
  fullMarkdownContent?: string;
  // Props for the external cache
  cache: Record<string, React.ReactNode>;
  setCacheEntry: (key: string, value: React.ReactNode) => void;
  [key: string]: any;
}>;

/**
 * A dedicated component to render code elements.
 * This allows using hooks to add advanced interactivity like the custom
 * "select all" feature and syntax highlighting.
 */
const CodeBlock: React.FC<CodeBlockProps> = ({
  node,
  inline,
  className,
  children,
  isGloballyStreaming = false,
  fullMarkdownContent = '',
  cache,
  setCacheEntry,
  ...props
}) => {
  const containerRef = React.useRef<HTMLDivElement>(null);
  const codeRef = React.useRef<HTMLElement | null>(null);
  const scrollerRef = React.useRef<HTMLDivElement>(null);

  // Effect to handle Ctrl+A (or Cmd+A) for selecting only the code block's content.
  React.useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleSelectAll = (event: KeyboardEvent) => {
      const isSelectAll = (event.ctrlKey || event.metaKey) && event.key === 'a';

      // Fire only if the user presses Ctrl+A and their focus is inside this code block.
      if (isSelectAll && container.contains(document.activeElement)) {
        event.preventDefault(); // Stop the browser from selecting the whole page.

        // Programmatically select the text inside the `code` element.
        if (codeRef.current) {
          const selection = window.getSelection();
          const range = document.createRange();
          range.selectNodeContents(codeRef.current);
          if (selection) {
            selection.removeAllRanges();
            selection.addRange(range);
          }
        }
      }
    };

    document.addEventListener('keydown', handleSelectAll);
    return () => {
      document.removeEventListener('keydown', handleSelectAll);
    };
  }, []); // Runs once on component mount.

  const isThisBlockStreaming = React.useMemo(() => {
    // If the entire message is not streaming, then this block isn't either.
    if (!isGloballyStreaming || !node?.position) {
      return false;
    }
    // A block is considered "streaming" if it's the last significant piece of content.
    const thisBlockEndOffset = node.position.end.offset;
    const contentAfterThisBlock =
      fullMarkdownContent.substring(thisBlockEndOffset);
    return contentAfterThisBlock.trim().length === 0;
  }, [isGloballyStreaming, fullMarkdownContent, node]);

  // Effect to auto-scroll the code block during streaming
  React.useEffect(() => {
    if (isThisBlockStreaming && scrollerRef.current) {
      const scroller = scrollerRef.current;
      scroller.scrollTop = scroller.scrollHeight;
    }
  }, [children, isThisBlockStreaming]); // Rerun when content changes during streaming

  const match = /language-(\w+)/.exec(className || '');
  const language = match ? match[1] : undefined;
  const code = String(children).replace(/\n$/, '');
  const cacheKey = code; // Use the code itself as the key for the cache

  const liveHighlightedCode = useShikiHighlighter(
    code,
    language,
    'github-light',
    {
      delay: 100,
    },
  );

  // Effect to update the external cache when a new highlighted version is ready.
  React.useEffect(() => {
    if (liveHighlightedCode) {
      setCacheEntry(cacheKey, liveHighlightedCode);
    }
  }, [liveHighlightedCode, cacheKey, setCacheEntry]);

  const codeContent = React.useMemo(() => {
    const cachedHtml = cache[cacheKey];

    // Priority 1: Use the live highlighted code if it's available. It's the most current.
    if (liveHighlightedCode) {
      return (
        <div ref={codeRef as React.RefObject<HTMLDivElement>}>
          {liveHighlightedCode}
        </div>
      );
    }

    // Priority 2: If live is pending, use the cached version to prevent flicker.
    if (cachedHtml) {
      return (
        <div ref={codeRef as React.RefObject<HTMLDivElement>}>{cachedHtml}</div>
      );
    }

    // Priority 3: Fallback to un-styled text only if there is no live or cached version.
    return (
      <pre
        className="font-mono text-sm leading-relaxed"
        ref={codeRef as React.RefObject<HTMLPreElement>}
      >
        <code>{code}</code>
      </pre>
    );
  }, [liveHighlightedCode, cache, cacheKey, code, codeRef]);

  // Render inline `<code>` tags for non-fenced code.
  if (inline || !match) {
    return (
      <code
        className="bg-muted text-muted-foreground rounded-sm px-1.5 py-1 font-mono text-sm"
        {...props}
      >
        {children}
      </code>
    );
  }

  // Render the full, interactive code block for fenced code.
  return (
    <div
      ref={containerRef}
      className="code-block-container my-2 flex max-h-[60vh] flex-col overflow-hidden rounded-md bg-secondary text-secondary-foreground"
      tabIndex={-1}
    >
      <div className="z-10 flex flex-shrink-0 items-center justify-between border-b border-border bg-secondary px-4 py-2">
        <span className="text-xs capitalize text-muted-foreground">
          {language || 'text'}
        </span>
        <button
          onClick={() => navigator.clipboard.writeText(code)}
          disabled={isThisBlockStreaming}
          className={cn(
            'rounded p-1 text-xs text-muted-foreground transition-colors disabled:opacity-50',
            // Hover effect is disabled as long as the entire message is streaming.
            !isGloballyStreaming &&
              'hover:bg-accent hover:text-accent-foreground',
          )}
        >
          Copy
        </button>
      </div>
      <div ref={scrollerRef} className="code-block-scroller overflow-y-auto">
        <div className="p-4 shiki-wrapper">{codeContent}</div>
      </div>
    </div>
  );
};

/**
 * A wrapper for tables to add interactivity like `Ctrl+A` to select
 * the whole table and custom copy-to-clipboard functionality.
 */
const TableWrapper: React.FC<React.PropsWithChildren<{ node?: any }>> = ({
  node,
  children,
  ...props
}) => {
  const containerRef = React.useRef<HTMLDivElement>(null);
  const tableRef = React.useRef<HTMLTableElement | null>(null);

  React.useEffect(() => {
    if (containerRef.current) {
      tableRef.current = containerRef.current.querySelector('table');
    }
  }, [children]);

  React.useEffect(() => {
    const container = containerRef.current;
    if (!container) return;
    const handleKeyDown = (event: KeyboardEvent) => {
      const isSelectAll = (event.ctrlKey || event.metaKey) && event.key === 'a';
      if (isSelectAll && container.contains(document.activeElement)) {
        event.preventDefault();
        if (tableRef.current) {
          const selection = window.getSelection();
          const range = document.createRange();
          range.selectNodeContents(tableRef.current);
          if (selection) {
            selection.removeAllRanges();
            selection.addRange(range);
          }
        }
      }
    };
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  React.useEffect(() => {
    const container = containerRef.current;
    if (!container) return;
    const handleCopy = (event: ClipboardEvent) => {
      const selection = window.getSelection();
      if (
        !selection ||
        selection.rangeCount === 0 ||
        !tableRef.current ||
        !event.clipboardData
      ) {
        return;
      }
      const range = selection.getRangeAt(0);
      if (!tableRef.current.contains(range.commonAncestorContainer)) {
        return;
      }
      const selectedText = selection.toString().trim();
      const fullTableText = tableRef.current.innerText.trim();
      if (selectedText === fullTableText) {
        event.preventDefault();
        event.clipboardData.setData('text/html', tableRef.current.outerHTML);
        event.clipboardData.setData('text/plain', tableRef.current.innerText);
        toast.success('Table copied as rich text!');
      }
    };
    container.addEventListener('copy', handleCopy);
    return () => container.removeEventListener('copy', handleCopy);
  }, []);

  return (
    <div
      ref={containerRef}
      className="my-4 overflow-x-auto focus-within:outline-none focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2 rounded-md"
      tabIndex={-1}
    >
      <table className="w-full border-collapse" {...props}>
        {children}
      </table>
    </div>
  );
};

/**
 * A component that renders markdown content, including LaTeX math expressions.
 * It leverages react-markdown with remark-math and rehype-katex.
 * This component is streaming-aware; incomplete markdown or LaTeX will be
 * rendered as plain text until it becomes valid.
 */
const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({
  content,
  isStreaming = false,
}) => {
  // A ref to hold the cache. This persists across re-renders of MarkdownRenderer,
  // preventing the cache from being lost if CodeBlock instances are unmounted.
  const highlightCache = React.useRef<Record<string, React.ReactNode>>({});

  const components = React.useMemo(
    () => ({
      p: ({ node, ...props }: React.PropsWithChildren<{ node?: any }>) => (
        <p className="mb-2 last:mb-0" {...props} />
      ),
      strong: ({ node, ...props }: React.PropsWithChildren<{ node?: any }>) => (
        <strong className="font-semibold" {...props} />
      ),
      a: ({ node, ...props }: React.PropsWithChildren<{ node?: any }>) => (
        <a
          className="text-primary hover:underline"
          target="_blank"
          rel="noopener noreferrer"
          {...props}
        />
      ),
      ul: ({ node, ...props }: React.PropsWithChildren<{ node?: any }>) => (
        <ul className="list-disc list-outside my-2 pl-6" {...props} />
      ),
      ol: ({ node, ...props }: React.PropsWithChildren<{ node?: any }>) => (
        <ol className="list-decimal list-outside my-2 pl-6" {...props} />
      ),
      li: ({ node, ...props }: React.PropsWithChildren<{ node?: any }>) => (
        <li className="mb-1" {...props} />
      ),
      table: TableWrapper,
      thead: ({ node, ...props }: React.PropsWithChildren<{ node?: any }>) => (
        <thead {...props} />
      ),
      tbody: ({ node, ...props }: React.PropsWithChildren<{ node?: any }>) => (
        <tbody {...props} />
      ),
      tr: ({ node, ...props }: React.PropsWithChildren<{ node?: any }>) => (
        <tr {...props} />
      ),
      th: ({ node, ...props }: React.PropsWithChildren<{ node?: any }>) => (
        <th className="px-4 py-2 text-left" {...props} />
      ),
      td: ({ node, ...props }: React.PropsWithChildren<{ node?: any }>) => (
        <td className="px-4 py-2" {...props} />
      ),
      code: (props: Omit<CodeBlockProps, 'cache' | 'setCacheEntry'>) => (
        <CodeBlock
          {...props}
          isGloballyStreaming={isStreaming}
          fullMarkdownContent={content}
          cache={highlightCache.current}
          setCacheEntry={(key, value) => {
            highlightCache.current[key] = value;
          }}
        />
      ),
    }),
    [isStreaming, content],
  );

  return (
    <ReactMarkdown
      remarkPlugins={[remarkMath, remarkGfm]}
      rehypePlugins={[
        rehypeRaw,
        [rehypeSanitize, tableSanitizeSchema],
        rehypeKatex,
      ]}
      components={components}
    >
      {content}
    </ReactMarkdown>
  );
};

export default React.memo(MarkdownRenderer);
