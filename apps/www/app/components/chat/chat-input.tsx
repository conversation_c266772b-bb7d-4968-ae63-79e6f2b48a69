import * as React from 'react';
import { Send } from 'lucide-react';
import { Button } from '~/components/ui/button';
import { AutoTextarea, type AutoTextareaRef } from '~/components/auto-textarea';
import { cn } from '~/lib/utils';

const HINT_CHAR_LIMIT = 45;

interface ChatInputProps {
  inputValue: string;
  handleInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  handleEnterKeyPress: () => void;
  handleSendMessage: () => void;
  textareaRef: React.Ref<AutoTextareaRef>;
  isSmallScreen: boolean;
  isAiTyping: boolean;
}

export function ChatInput({
  inputValue,
  handleInputChange,
  handleEnterKeyPress,
  handleSendMessage,
  textareaRef,
  isSmallScreen,
  isAiTyping,
}: ChatInputProps) {
  const [discoveredShortcuts, setDiscoveredShortcuts] = React.useState({
    singleLine: false,
    multiLine: false,
  });

  // Effect to load shortcut discovery state from sessionStorage
  React.useEffect(() => {
    const singleLine =
      sessionStorage.getItem('singleLineShortcutDiscovered') === 'true';
    const multiLine =
      sessionStorage.getItem('multiLineShortcutDiscovered') === 'true';
    setDiscoveredShortcuts({ singleLine, multiLine });
  }, []);

  // This handler is ONLY for tracking when to hide the hints.
  // It should not prevent default or trigger a send.
  const handleTutorialDiscovery = (
    e: React.KeyboardEvent<HTMLTextAreaElement>,
  ) => {
    if (e.key === 'Enter') {
      const isMultiLine = inputValue.includes('\n');

      // Condition for completing single-line tutorial:
      // Plain Enter on a single line.
      if (!isMultiLine && !e.shiftKey && !e.metaKey && !e.ctrlKey) {
        if (!discoveredShortcuts.singleLine) {
          sessionStorage.setItem('singleLineShortcutDiscovered', 'true');
          setDiscoveredShortcuts((prev) => ({ ...prev, singleLine: true }));
        }
      }

      // Condition for completing multi-line tutorial:
      // Ctrl+Enter on a multi-line message.
      if (isMultiLine && (e.metaKey || e.ctrlKey)) {
        if (!discoveredShortcuts.multiLine) {
          sessionStorage.setItem('multiLineShortcutDiscovered', 'true');
          setDiscoveredShortcuts((prev) => ({ ...prev, multiLine: true }));
        }
      }
    }
  };

  const isTextareaExpanded = React.useMemo(
    () => inputValue.split('\n').length > 1 || inputValue.length > 50,
    [inputValue],
  );

  const isLastLineTooLong =
    (inputValue.split('\n').pop()?.length || 0) > HINT_CHAR_LIMIT;

  return (
    <div
      className={cn(
        'min-h-16 relative transition-all duration-200 border-t',
        isTextareaExpanded
          ? 'bg-background shadow-lg shadow-black/5 border-transparent'
          : 'border-border',
      )}
    >
      {isTextareaExpanded && (
        <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-border to-transparent opacity-30" />
      )}
      <div className="mx-auto max-w-4xl h-full flex items-end pb-3 pl-8 pr-6 lg:px-5">
        <div className="flex items-end gap-3 w-full">
          {/* Avatar placeholder is only shown on larger screens to align with message content */}
          {!isSmallScreen && <div className="h-8 w-8 shrink-0" />}
          <div className="flex-1 flex gap-2 items-end w-full">
            <div className="relative flex-1">
              <AutoTextarea
                ref={textareaRef}
                value={inputValue}
                onChange={handleInputChange}
                onKeyDown={handleTutorialDiscovery}
                onEnterKeyPress={handleEnterKeyPress}
                placeholder="Digite sua pergunta sobre FUVEST..."
                className={cn(
                  'flex-1 max-h-40 border-input focus:border-ring focus:ring-1 focus:ring-ring focus:ring-offset-0 focus:ring-inset',
                  !isSmallScreen && 'pr-10',
                )}
              />
              {inputValue.length > 0 &&
                !isLastLineTooLong &&
                !isSmallScreen &&
                !isAiTyping && (
                  <div className="absolute bottom-2 right-2 flex items-center gap-2 text-xs text-muted-foreground">
                    {inputValue.includes('\n') ? (
                      !discoveredShortcuts.multiLine && (
                        <>
                          <span className="bg-muted px-1.5 py-0.5 rounded">
                            Enter para nova linha
                          </span>
                          <span className="bg-muted px-1.5 py-0.5 rounded">
                            Ctrl+Enter para enviar
                          </span>
                        </>
                      )
                    ) : !discoveredShortcuts.singleLine ? (
                      <>
                        <span className="bg-muted px-1.5 py-0.5 rounded">
                          Shift+Enter para nova linha
                        </span>
                        <span className="bg-muted px-1.5 py-0.5 rounded">
                          Enter para enviar
                        </span>
                      </>
                    ) : null}
                  </div>
                )}
            </div>
            <Button
              onClick={handleSendMessage}
              disabled={!inputValue.trim() || isAiTyping}
              className={cn(
                'h-9 w-9 p-2 shrink-0 rounded-lg transition-all duration-200',
                inputValue.trim() && !isAiTyping
                  ? 'bg-primary text-primary-foreground shadow-md hover:shadow-lg hover:bg-primary/90'
                  : 'bg-muted text-muted-foreground cursor-not-allowed',
              )}
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
