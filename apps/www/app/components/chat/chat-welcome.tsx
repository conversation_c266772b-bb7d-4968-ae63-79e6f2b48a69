// apps/www/app/components/chat/chat-welcome.tsx
import { Button } from '~/components/ui/button';
import { useChatStore } from '~/store/chatStore';

const examplePrompts = [
  {
    heading: 'Explique um conceito',
    text: 'Me explique a Segunda Lei de Newton em termos simples.',
  },
  {
    heading: 'Seja um parceiro de escrita',
    text: 'Redija um parágrafo introdutório sobre a crise hídrica no Brasil.',
  },
  {
    heading: 'Resuma um tópico',
    text: 'Quais foram as principais causas e consequências da Revolução Francesa?',
  },
  {
    heading: 'Ajude-me a planejar',
    text: 'Crie um plano de estudos de 1 semana para química orgânica.',
  },
];

export function ChatWelcome({ isSmallScreen }: { isSmallScreen: boolean }) {
  const { setInputValue } = useChatStore.getState();

  const handlePromptClick = (prompt: string) => {
    setInputValue(prompt);
  };

  return (
    <div className="flex-1 overflow-y-auto pt-20 lg:pt-28">
      {/* This container sets the content width and horizontal padding */}
      <div className="mx-auto max-w-4xl pl-8 pr-6 lg:px-6">
        {/*
          This flex container is the key. It creates an indent on the left
          that matches the chat messages and the input field.
        */}
        <div className="flex w-full items-start gap-3">
          {/* This empty div is a placeholder, hidden on mobile */}
          {!isSmallScreen && <div className="h-8 w-8 shrink-0" />}

          {/* All content now sits in this single column, perfectly aligned with the input below. */}
          <div className="flex-1">
            <h1 className="text-5xl lg:text-6xl font-sans font-black tracking-tighter text-foreground mb-2">
              START A NEW CONVERSATION
            </h1>
            <p className="font-sans text-sm text-muted-foreground uppercase tracking-widest mb-16">
              Begin your chat by entering a prompt
            </p>

            {/* The grid of prompts is also inside this indented column */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-left">
              {examplePrompts.map((prompt, index) => (
                <Button
                  key={index}
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-start justify-start text-left whitespace-normal"
                  onClick={() => handlePromptClick(prompt.text)}
                >
                  <div className="text-sm font-semibold text-foreground mb-1">
                    {prompt.heading}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {prompt.text}
                  </div>
                </Button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
