// apps/www/app/components/chat/message-list.tsx
import * as React from 'react';
import {
  Copy,
  Edit3,
  Trash2,
  Check,
  X,
  Rotate<PERSON>cw,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react';
import { useVirtualizer } from '@tanstack/react-virtual';
import { Button } from '~/components/ui/button';
import { Textarea } from '~/components/ui/textarea';
import { Avatar, AvatarFallback } from '~/components/ui/avatar';
import { Skeleton } from '~/components/ui/skeleton';
import { cn } from '~/lib/utils';
import { formatMessageDate } from '~/lib/date-utils';
import type { ChatMessage } from '~/types/chat.ts';
import { useChatActions } from '~/hooks/useChatActions';
import MarkdownRenderer from './markdown-renderer';

// Define the actions that MessageComponent will need
interface MessageActions {
  saveEdit: (messageId: string, newContent: string) => void;
  copyMessage: (content: string) => void;
  deleteMessage: (messageId: string) => void;
  regenerateResponse: (aiMessageId: string) => void;
  navigateSibling: (messageId: string, direction: 'next' | 'prev') => void;
}

interface MessageComponentProps {
  message: ChatMessage;
  actions: MessageActions; // Actions are now passed as a prop
  isLastMessage?: boolean;
  isStreaming?: boolean;
  isAiTyping?: boolean;
  isSmallScreen: boolean;
}

const MessageComponent = React.memo(function MessageComponent({
  message,
  actions, // Destructure actions from props
  isLastMessage = false,
  isStreaming = false,
  isAiTyping = false,
  isSmallScreen,
}: MessageComponentProps) {
  const [isEditing, setIsEditing] = React.useState(false);
  const [editValue, setEditValue] = React.useState(message.content);
  const [isConfirmingDelete, setIsConfirmingDelete] = React.useState(false);
  const textareaRef = React.useRef<HTMLTextAreaElement>(null);
  const deleteButtonRef = React.useRef<HTMLButtonElement>(null);

  // Effect to handle focusing the textarea in edit mode
  React.useEffect(() => {
    if (isEditing) {
      const timerId = setTimeout(() => {
        const textarea = textareaRef.current;
        if (textarea) {
          textarea.focus();
          textarea.setSelectionRange(
            textarea.value.length,
            textarea.value.length,
          );
        }
      }, 0);
      return () => clearTimeout(timerId);
    }
  }, [isEditing]);

  // Effect to handle clicking away from the delete confirmation
  React.useEffect(() => {
    if (!isConfirmingDelete) return;

    const handleClickOutside = (event: MouseEvent) => {
      if (
        deleteButtonRef.current &&
        !deleteButtonRef.current.contains(event.target as Node)
      ) {
        setIsConfirmingDelete(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isConfirmingDelete]);

  const handleSave = () => {
    if (editValue.trim() !== message.content.trim()) {
      actions.saveEdit(message.id, editValue);
    }
    setIsEditing(false);
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditValue(message.content);
  };

  const handleEdit = () => {
    setIsEditing(true);
    setIsConfirmingDelete(false); // Ensure delete confirmation is reset
  };

  const handleDeleteClick = () => {
    if (isConfirmingDelete) {
      // Perform the delete action. No need to reset state, as the
      // component will be unmounted.
      actions.deleteMessage(message.id);
    } else {
      setIsConfirmingDelete(true);
    }
  };

  const handleEditKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      handleSave();
      return;
    }
    if (e.key === 'Escape') {
      e.preventDefault();
      handleCancel();
      return;
    }
  };

  const isDeleted = !!message.deletedAt;
  const isAI = message.role === 'ASSISTANT';

  // --- Mobile Layout ---
  if (isSmallScreen) {
    return (
      <div
        id={message.id}
        className={cn(
          'w-full group transition-colors',
          !isLastMessage && !isStreaming && 'border-b border-border',
          isDeleted
            ? 'bg-[oklch(0.97_0.015_20)]'
            : isAI
              ? 'bg-[var(--assistant-message-bg)] hover:bg-[var(--assistant-message-hover-bg)]'
              : 'bg-background hover:bg-muted',
        )}
      >
        <div className="mx-auto max-w-4xl pl-8 pr-6 py-3">
          <div className="flex flex-col gap-2">
            {/* Header: Avatar, Name, Sibling Nav */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2.5">
                <Avatar className="h-6 w-6">
                  <AvatarFallback
                    className={cn(
                      'text-xs font-medium',
                      isAI
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-secondary text-secondary-foreground',
                    )}
                  >
                    {isAI ? 'AI' : 'U'}
                  </AvatarFallback>
                </Avatar>
                <div className="flex items-baseline gap-2">
                  <span className="text-sm font-medium text-muted-foreground">
                    {isAI ? 'FUVEST AI' : 'Você'}
                  </span>
                  {isAI && message.model && !isDeleted && (
                    <span className="text-xs text-muted-foreground bg-muted-foreground/10 px-1.5 py-0.5 rounded">
                      {message.model}
                    </span>
                  )}
                </div>
              </div>
              {message.siblingCount > 1 && (
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 px-1 text-muted-foreground hover:text-foreground"
                    disabled={message.siblingPosition <= 1 || isAiTyping}
                    onClick={() => actions.navigateSibling(message.id, 'prev')}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <span className="font-mono tabular-nums">
                    {message.siblingPosition}/{message.siblingCount}
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 px-1 text-muted-foreground hover:text-foreground"
                    disabled={
                      message.siblingPosition >= message.siblingCount ||
                      isAiTyping
                    }
                    onClick={() => actions.navigateSibling(message.id, 'next')}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>

            {/* Content & Footer */}
            <div className="flex-1">
              <div className="flex-grow">
                {isEditing ? (
                  <Textarea
                    ref={textareaRef}
                    value={editValue}
                    onChange={(e) => setEditValue(e.target.value)}
                    onKeyDown={handleEditKeyDown}
                    className="flex-1 min-h-[60px] text-sm"
                    disabled={isAiTyping}
                  />
                ) : (
                  <div
                    className={cn(
                      'relative text-sm leading-relaxed text-foreground break-words [hyphens:auto]',
                      isDeleted && 'text-muted-foreground italic',
                    )}
                  >
                    {isDeleted ? (
                      'This message was deleted on this branch.'
                    ) : (
                      <MarkdownRenderer
                        content={message.content}
                        isStreaming={isStreaming}
                      />
                    )}
                  </div>
                )}
              </div>
              <div
                className={cn(
                  'flex items-center pt-2',
                  isEditing ? 'justify-end' : 'justify-between',
                )}
              >
                {isEditing ? (
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleCancel}
                      disabled={isAiTyping}
                    >
                      <X className="h-3 w-3 mr-1" />
                      Cancelar
                    </Button>
                    <Button
                      size="sm"
                      onClick={handleSave}
                      disabled={isAiTyping}
                    >
                      <Check className="h-3 w-3 mr-1" />
                      Salvar
                    </Button>
                  </div>
                ) : (
                  <>
                    <span className="text-xs text-muted-foreground">
                      {formatMessageDate(message.createdAt)}
                    </span>
                    {!isDeleted && (
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6"
                          onClick={() => actions.copyMessage(message.content)}
                          disabled={isAiTyping}
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                        {isAI && (
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6"
                            onClick={() =>
                              actions.regenerateResponse(message.id)
                            }
                            disabled={isAiTyping}
                          >
                            <RotateCcw className="h-3 w-3" />
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6"
                          onClick={handleEdit}
                          disabled={isAiTyping}
                        >
                          <Edit3 className="h-3 w-3" />
                        </Button>
                        <Button
                          ref={deleteButtonRef}
                          variant="ghost"
                          size="icon"
                          onClick={handleDeleteClick}
                          disabled={isAiTyping}
                          className={cn(
                            'h-6 w-6 text-muted-foreground transition-colors hover:text-destructive',
                            isConfirmingDelete &&
                              'bg-destructive/20 text-destructive',
                          )}
                        >
                          {isConfirmingDelete ? (
                            <Check className="h-4 w-4" />
                          ) : (
                            <Trash2 className="h-3 w-3" />
                          )}
                        </Button>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // --- Desktop Layout (Original) ---
  return (
    <div
      id={message.id}
      className={cn(
        'w-full group transition-colors',
        !isLastMessage && !isStreaming && 'border-b border-border',
        isDeleted
          ? 'bg-[oklch(0.97_0.015_20)] hover:bg-[oklch(0.95_0.02_20)]'
          : isAI
            ? 'bg-[var(--assistant-message-bg)] hover:bg-[var(--assistant-message-hover-bg)]'
            : 'bg-background hover:bg-muted',
      )}
    >
      <div className="mx-auto max-w-4xl px-6 py-4">
        <div className="flex gap-3 items-start">
          <Avatar className="h-8 w-8 shrink-0">
            <AvatarFallback
              className={cn(
                'text-xs font-medium',
                isAI
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-secondary text-secondary-foreground',
              )}
            >
              {isAI ? 'AI' : 'U'}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0 flex flex-col">
            <div className="flex items-center justify-between mb-1">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-muted-foreground">
                  {isAI ? 'FUVEST AI' : 'Você'}
                </span>
                {isAI && message.model && !isDeleted && (
                  <span className="text-xs text-muted-foreground bg-muted px-1.5 py-0.5 rounded">
                    {message.model}
                  </span>
                )}
              </div>
              {message.siblingCount > 1 && (
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 px-1 text-muted-foreground hover:text-foreground"
                    disabled={message.siblingPosition <= 1 || isAiTyping}
                    onClick={() => actions.navigateSibling(message.id, 'prev')}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <span className="font-mono tabular-nums">
                    {message.siblingPosition}/{message.siblingCount}
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 px-1 text-muted-foreground hover:text-foreground"
                    disabled={
                      message.siblingPosition >= message.siblingCount ||
                      isAiTyping
                    }
                    onClick={() => actions.navigateSibling(message.id, 'next')}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>

            <div className="flex-1">
              <div className="flex-grow">
                {isEditing ? (
                  <Textarea
                    ref={textareaRef}
                    value={editValue}
                    onChange={(e) => setEditValue(e.target.value)}
                    onKeyDown={handleEditKeyDown}
                    className="flex-1 min-h-[60px] text-sm"
                    disabled={isAiTyping}
                  />
                ) : (
                  <div
                    className={cn(
                      'relative text-sm leading-relaxed text-foreground pr-11 break-words [hyphens:auto]',
                      isDeleted && 'text-muted-foreground italic',
                    )}
                  >
                    {isDeleted ? (
                      'This message was deleted on this branch.'
                    ) : (
                      <MarkdownRenderer
                        content={message.content}
                        isStreaming={isStreaming}
                      />
                    )}
                  </div>
                )}
              </div>

              <div
                className={cn(
                  'sticky message-footer bottom-0 z-10 flex items-center justify-between pt-2 transition-colors',
                  isDeleted
                    ? 'bg-[oklch(0.97_0.015_20)] group-hover:bg-[oklch(0.95_0.02_20)]'
                    : isAI
                      ? 'bg-[var(--assistant-message-bg)] group-hover:bg-[var(--assistant-message-hover-bg)]'
                      : 'bg-background group-hover:bg-muted',
                )}
              >
                {isEditing ? (
                  <>
                    <span /> {/* Empty span to push buttons to the right */}
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={handleCancel}
                        disabled={isAiTyping}
                      >
                        <X className="h-3 w-3 mr-1" />
                        Cancelar
                      </Button>
                      <Button
                        size="sm"
                        onClick={handleSave}
                        disabled={isAiTyping}
                      >
                        <Check className="h-3 w-3 mr-1" />
                        Salvar
                      </Button>
                    </div>
                  </>
                ) : (
                  <>
                    <span className="text-xs text-muted-foreground">
                      {formatMessageDate(message.createdAt)}
                    </span>
                    {!isDeleted && (
                      <div
                        className={cn(
                          'flex items-center gap-1 transition-opacity duration-200',
                          isLastMessage
                            ? 'opacity-100'
                            : 'opacity-100 lg:opacity-0 lg:group-hover:opacity-100',
                        )}
                      >
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6"
                          onClick={() => actions.copyMessage(message.content)}
                          disabled={isAiTyping}
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                        {isAI && (
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6"
                            onClick={() =>
                              actions.regenerateResponse(message.id)
                            }
                            disabled={isAiTyping}
                          >
                            <RotateCcw className="h-3 w-3" />
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6"
                          onClick={handleEdit}
                          disabled={isAiTyping}
                        >
                          <Edit3 className="h-3 w-3" />
                        </Button>
                        <Button
                          ref={deleteButtonRef}
                          variant="ghost"
                          size="icon"
                          onClick={handleDeleteClick}
                          disabled={isAiTyping}
                          className={cn(
                            'h-6 w-6 text-muted-foreground transition-colors hover:text-destructive',
                            isConfirmingDelete &&
                              'bg-destructive/20 text-destructive',
                          )}
                        >
                          {isConfirmingDelete ? (
                            <Check className="h-4 w-4" />
                          ) : (
                            <Trash2 className="h-3 w-3" />
                          )}
                        </Button>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

const SkeletonMessage = React.memo(function SkeletonMessage() {
  return (
    <div className="w-full bg-muted">
      <div className="mx-auto max-w-4xl px-6 py-4">
        <div className="flex gap-3">
          <Avatar className="h-8 w-8 shrink-0">
            <AvatarFallback className="bg-primary/20 text-primary-foreground text-xs font-medium">
              AI
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <span className="text-sm font-medium text-muted-foreground">
                FUVEST AI
              </span>
            </div>
            <div className="space-y-2 pr-11">
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

interface MessageListProps {
  messages: ChatMessage[];
  streamingResponse: ChatMessage | null;
  isInitialLoadRef: React.MutableRefObject<boolean>;
  isAiTyping: boolean;
  isSmallScreen: boolean;
}

export function MessageList({
  messages,
  streamingResponse,
  isInitialLoadRef,
  isAiTyping,
  isSmallScreen,
}: MessageListProps) {
  const chatActions = useChatActions();
  const listContainerRef = React.useRef<HTMLDivElement>(null);
  const messagesEndRef = React.useRef<HTMLDivElement>(null);
  const [shouldAutoScroll, setShouldAutoScroll] = React.useState(true);

  React.useEffect(() => {
    const scrollElement = listContainerRef.current;
    if (!scrollElement) return;
    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = scrollElement;
      const isAtBottom = scrollHeight - scrollTop - clientHeight < 50;
      setShouldAutoScroll(isAtBottom);
    };
    scrollElement.addEventListener('scroll', handleScroll, { passive: true });
    return () => scrollElement.removeEventListener('scroll', handleScroll);
  }, []);

  React.useLayoutEffect(() => {
    const hasContent = messages.length > 0 || streamingResponse;
    if (!hasContent || !shouldAutoScroll) return;

    const isStreaming = !!streamingResponse?.content;
    const behavior =
      isInitialLoadRef.current || isStreaming ? 'auto' : 'smooth';

    messagesEndRef.current?.scrollIntoView({ behavior, block: 'end' });

    if (isInitialLoadRef.current) {
      isInitialLoadRef.current = false;
    }
  }, [messages, streamingResponse, shouldAutoScroll, isInitialLoadRef]);

  return (
    <div ref={listContainerRef} className="flex-1 overflow-y-scroll">
      <div className="w-full">
        {messages.map((message, index) => (
          <MessageComponent
            key={`${message.id}-${message.siblingPosition}`}
            message={message}
            isLastMessage={index === messages.length - 1 && !streamingResponse}
            isStreaming={false}
            isAiTyping={isAiTyping}
            actions={chatActions}
            isSmallScreen={isSmallScreen}
          />
        ))}
        {streamingResponse && (
          <>
            {streamingResponse.content.length === 0 ? (
              <SkeletonMessage />
            ) : (
              <MessageComponent
                message={streamingResponse}
                isLastMessage={false}
                isStreaming={true}
                isAiTyping={isAiTyping}
                actions={chatActions}
                isSmallScreen={isSmallScreen}
              />
            )}
          </>
        )}
        <div ref={messagesEndRef} />
      </div>
    </div>
  );
}

export function SmartMessageList({
  messages,
  streamingResponse,
  isInitialLoadRef,
  isAiTyping,
  isSmallScreen,
}: MessageListProps) {
  const shouldVirtualize = messages.length >= 50;

  if (shouldVirtualize) {
    return (
      <VirtualizedMessageList
        messages={messages}
        streamingResponse={streamingResponse}
        isInitialLoadRef={isInitialLoadRef}
        isAiTyping={isAiTyping}
        isSmallScreen={isSmallScreen}
      />
    );
  }

  return (
    <MessageList
      messages={messages}
      streamingResponse={streamingResponse}
      isInitialLoadRef={isInitialLoadRef}
      isAiTyping={isAiTyping}
      isSmallScreen={isSmallScreen}
    />
  );
}

export function VirtualizedMessageList({
  messages,
  streamingResponse,
  isInitialLoadRef,
  isAiTyping,
  isSmallScreen,
}: MessageListProps) {
  const chatActions = useChatActions();
  const parentRef = React.useRef<HTMLDivElement>(null);
  const messagesEndRef = React.useRef<HTMLDivElement>(null);

  const allItems = React.useMemo(() => {
    const items = [...messages];
    if (streamingResponse) {
      items.push(streamingResponse);
    }
    return items;
  }, [messages, streamingResponse]);

  const estimateSize = React.useCallback(
    (index: number) => {
      const message = allItems[index];
      if (!message) return 120;
      let estimatedHeight = 80;
      const contentLength = message.content.length;
      const estimatedLines = Math.max(1, Math.ceil(contentLength / 80));
      estimatedHeight += estimatedLines * 24;
      const codeBlockMatches = message.content.match(/```[\s\S]*?```/g) || [];
      const mathBlockMatches = message.content.match(/\$\$[\s\S]*?\$\$/g) || [];
      estimatedHeight +=
        codeBlockMatches.length * 100 + mathBlockMatches.length * 60;
      const listMatches = message.content.match(/^[\s]*[-*+]\s/gm) || [];
      estimatedHeight += listMatches.length * 28;
      return Math.max(120, estimatedHeight);
    },
    [allItems],
  );

  const virtualizer = useVirtualizer({
    count: allItems.length,
    getScrollElement: () => parentRef.current,
    estimateSize,
    overscan: 8,
    measureElement: (element) => {
      return element?.getBoundingClientRect().height ?? estimateSize(0);
    },
    scrollMargin: parentRef.current?.offsetTop ?? 0,
    gap: 0,
  });

  const [shouldAutoScroll, setShouldAutoScroll] = React.useState(true);

  React.useEffect(() => {
    const scrollElement = parentRef.current;
    if (!scrollElement) return;
    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = scrollElement;
      const isAtBottom = scrollHeight - scrollTop - clientHeight < 50;
      setShouldAutoScroll(isAtBottom);
    };
    scrollElement.addEventListener('scroll', handleScroll, { passive: true });
    return () => scrollElement.removeEventListener('scroll', handleScroll);
  }, []);

  React.useEffect(() => {
    if (allItems.length > 0 && shouldAutoScroll) {
      const lastIndex = allItems.length - 1;
      const behavior = isInitialLoadRef.current ? 'auto' : 'smooth';
      virtualizer.scrollToIndex(lastIndex, { align: 'end', behavior });
      if (isInitialLoadRef.current) {
        isInitialLoadRef.current = false;
      }
    }
  }, [allItems.length, virtualizer, shouldAutoScroll, isInitialLoadRef]);

  React.useEffect(() => {
    if (streamingResponse && shouldAutoScroll) {
      const lastIndex = allItems.length - 1;
      virtualizer.scrollToIndex(lastIndex, { align: 'end', behavior: 'auto' });
    }
  }, [
    streamingResponse?.content,
    virtualizer,
    shouldAutoScroll,
    allItems.length,
  ]);

  return (
    <div ref={parentRef} className="flex-1 overflow-y-scroll">
      <div
        style={{
          height: `${virtualizer.getTotalSize()}px`,
          width: '100%',
          position: 'relative',
        }}
      >
        {virtualizer.getVirtualItems().map((virtualItem) => {
          const message = allItems[virtualItem.index];
          const isLastMessage =
            virtualItem.index === allItems.length - 1 && !streamingResponse;
          const isStreamingMessage = !!(
            streamingResponse && message.id === streamingResponse.id
          );
          return (
            <div
              key={`${message.id}-${message.siblingPosition}`}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                transform: `translateY(${virtualItem.start}px)`,
              }}
              ref={virtualizer.measureElement}
              data-index={virtualItem.index}
            >
              {isStreamingMessage && streamingResponse?.content.length === 0 ? (
                <SkeletonMessage />
              ) : (
                <MessageComponent
                  message={message}
                  isLastMessage={isLastMessage}
                  isStreaming={isStreamingMessage}
                  isAiTyping={isAiTyping}
                  actions={chatActions}
                  isSmallScreen={isSmallScreen}
                />
              )}
            </div>
          );
        })}
        <div ref={messagesEndRef} />
      </div>
    </div>
  );
}
