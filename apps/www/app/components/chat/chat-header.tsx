import * as React from 'react';
import { Menu, Plus } from 'lucide-react';
import { Button } from '~/components/ui/button';
import { useChatActions } from '~/hooks/useChatActions';
import { useUIStore } from '~/store/uiStore';

export function ChatHeader() {
  const { createNewThread } = useChatActions();
  const { toggleMobileSidebar } = useUIStore();

  return (
    <header className="h-14 border-b border-border px-6 flex items-center justify-between shrink-0">
      <div className="flex items-center gap-3 lg:hidden">
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8"
          onClick={toggleMobileSidebar}
        >
          <Menu className="h-4 w-4" />
        </Button>
        <h1 className="font-semibold text-sm">Chat IA</h1>
      </div>
      <div className="hidden lg:flex items-center gap-3">
        <h1 className="font-semibold text-sm">Chat IA</h1>
      </div>
      <div className="flex items-center gap-2">
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8"
          onClick={createNewThread}
        >
          <Plus className="h-4 w-4" />
        </Button>
      </div>
    </header>
  );
}
