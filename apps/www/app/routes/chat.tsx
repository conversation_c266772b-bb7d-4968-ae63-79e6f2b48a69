// apps/www/app/routes/chat.tsx
import { useEffect } from 'react';
import { Outlet, useParams } from 'react-router';
import { useShallow } from 'zustand/react/shallow';

import { ChatSidebar } from '~/components/chat/chat-sidebar';
import { useChatActions } from '~/hooks/useChatActions';
import { useMediaQuery } from '~/hooks/useMediaQuery';
import { useChatStore } from '~/store/chatStore';
import * as chatService from '~/services/chat-service';
import { useAuth } from '~/auth/useAuth';
import { useUIStore } from '~/store/uiStore';

export default function ChatLayout() {
  const { createNewThread } = useChatActions();
  const { isAuthenticated } = useAuth();
  const { isMobileSidebarOpen, setMobileSidebarOpen } = useUIStore();
  const isSmallScreen = useMediaQuery('(max-width: 1023px)');

  const { setThreads, setThreadsStatus } = useChatStore.getState();
  const { threads, threadsStatus, unreadThreads, inputValue } = useChatStore(
    useShallow((state) => ({
      threads: state.threads,
      threadsStatus: state.threadsStatus,
      unreadThreads: state.unreadThreads,
      inputValue: state.inputValue,
    })),
  );

  const { threadId: currentThreadId } = useParams<{ threadId?: string }>();

  // Effect to fetch threads list for the sidebar
  useEffect(() => {
    if (isAuthenticated && threadsStatus === 'idle') {
      setThreadsStatus('loading');
      chatService
        .fetchThreads()
        .then((fetchedThreads) => {
          setThreads(fetchedThreads);
          setThreadsStatus('success');
        })
        .catch((err) => {
          console.error('Failed to fetch threads:', err);
          setThreadsStatus('error');
        });
    }
  }, [isAuthenticated, threadsStatus, setThreads, setThreadsStatus]);

  const handleThreadSelect = () => {
    if (isSmallScreen) {
      setMobileSidebarOpen(false);
    }
  };

  const handleNewThreadClick = () => {
    createNewThread();
    if (isSmallScreen) {
      setMobileSidebarOpen(false);
    }
  };

  return (
    <div className="flex h-dvh bg-background overflow-hidden">
      <ChatSidebar
        threads={threads}
        currentThreadId={currentThreadId || null}
        onNewThread={handleNewThreadClick}
        unsentMessage={inputValue}
        unreadThreads={unreadThreads}
        onThreadSelect={handleThreadSelect}
      />
      {isSmallScreen && isMobileSidebarOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-30 lg:hidden"
          onClick={() => setMobileSidebarOpen(false)}
          aria-hidden="true"
        />
      )}
      <div className="flex-1 flex flex-col min-w-0">
        <Outlet />
      </div>
    </div>
  );
}
