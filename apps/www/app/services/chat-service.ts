// apps/www/app/services/chat-service.ts
import { apiClient } from '~/utils/axios-client';
import type { ChatMessage, Thread, ThreadTree } from '~/types/chat';

/**
 * Fetches all threads for the authenticated user.
 */
export const fetchThreads = async (): Promise<Thread[]> => {
  const response = await apiClient.get<{ data: Thread[] }>('/api/chat/threads');
  return response.data;
};

/**
 * Fetches the message history for a specific thread.
 * @param threadId - The ID of the thread to fetch.
 */
export const fetchMessages = async (
  threadId: string,
): Promise<ChatMessage[]> => {
  const response = await apiClient.get<{ data: ChatMessage[] }>(
    `/api/chat/history/${threadId}`,
  );
  return response.data;
};

/**
 * Fetches the message history using the optimized endpoint.
 * This is preferred over fetchMessages for better performance.
 * @param threadId - The ID of the thread to fetch.
 */
export const fetchMessagesOptimized = async (
  threadId: string,
): Promise<ChatMessage[]> => {
  const response = await apiClient.get<{ data: ChatMessage[] }>(
    `/api/chat/history/${threadId}`,
  );
  return response.data;
};

/**
 * Creates a new chat thread.
 * @param title - The initial title for the thread.
 */
export const createThread = async (title: string): Promise<Thread> => {
  const response = await apiClient.post<{ data: Thread }>('/api/chat/threads', {
    title,
  });
  return response.data;
};

/**
 * Sends a new message in a thread.
 * @param content - The content of the message.
 * @param threadId - The ID of the thread.
 * @param parentId - The ID of the parent message, if any.
 * @param idempotencyKey - A unique key to prevent duplicate messages.
 */
export const sendMessage = async (
  content: string,
  threadId: string,
  parentId: string | null,
  idempotencyKey: string,
): Promise<ChatMessage> => {
  const response = await apiClient.post<{ data: ChatMessage }>(
    '/api/chat/message',
    {
      content,
      threadId,
      parentId,
      idempotencyKey,
      model: 'gemini-2.5-flash-lite',
    },
  );
  return response.data;
};

/**
 * Edits an existing message, creating a new sibling branch.
 * @param messageId - The ID of the message to edit.
 * @param content - The new content for the message.
 * @returns The updated conversation history.
 */
export const editMessage = async (
  messageId: string,
  content: string,
): Promise<ThreadTree> => {
  const response = await apiClient.put<{ data: ThreadTree }>(
    `/api/chat/message/${messageId}`,
    { content },
  );
  return response.data;
};

/**
 * Deletes a message and its descendants.
 * @param messageId - The ID of the message to delete.
 */
export const deleteMessage = async (messageId: string): Promise<ThreadTree> => {
  const response = await apiClient.delete<{ data: ThreadTree }>(
    `/api/chat/message/${messageId}`,
  );
  return response.data;
};

/**
 * Persists a client-side navigation change to the server.
 * This is a fire-and-forget operation from the client's perspective.
 * @param messageId - The ID of the current message.
 * @param direction - The direction to navigate ('next' or 'prev').
 */
export const persistNavigation = async (
  messageId: string,
  direction: 'next' | 'prev',
): Promise<{ status: string }> => {
  const response = await apiClient.post<{ status: string }>(
    '/api/chat/navigate',
    { messageId, direction },
  );
  return response;
};

/**
 * Fetches the complete thread tree with all branches and messages.
 * This enables client-side navigation without server round-trips.
 * @param threadId - The ID of the thread to fetch.
 */
export const fetchThreadTree = async (
  threadId: string,
): Promise<ThreadTree> => {
  const response = await apiClient.get<{ data: ThreadTree }>(
    `/api/chat/thread-tree/${threadId}`,
  );
  return response.data;
};
