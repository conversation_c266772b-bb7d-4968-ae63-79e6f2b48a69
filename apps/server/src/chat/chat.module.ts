import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ChatController } from './chat.controller';
import { ChatService } from './services/chat.service';
import { ThreadTreeCacheService } from './services/thread-tree-cache.service';
import { OpenRouterService } from './services/openrouter.service';
import { GeminiService } from './services/gemini.service';
import { PrismaService } from '../prisma/prisma.service';
import { RedisModule } from '../redis/redis.module';

@Module({
  imports: [ConfigModule, RedisModule],
  controllers: [ChatController],
  providers: [
    ChatService,
    ThreadTreeCacheService,
    OpenRouterService,
    GeminiService,
    PrismaService,
  ],
})
export class ChatModule {}
