import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from '../../redis/redis.service';
import { PrismaService } from '../../prisma/prisma.service';
import { ThreadTreeDto, ThreadMessageDto } from '../dto/chat-message.dto';
import { Message } from '@prisma/client';

@Injectable()
export class ThreadTreeCacheService {
  private readonly logger = new Logger(ThreadTreeCacheService.name);
  private readonly CACHE_TTL = 60 * 60 * 24; // 24 hours
  private readonly CACHE_PREFIX = 'thread_tree:';

  constructor(
    private readonly redis: RedisService,
    private readonly prisma: PrismaService,
  ) {}

  /**
   * Get the complete thread tree with all branches and messages
   * Uses aggressive caching with Redis
   */
  async getThreadTree(
    userId: string,
    threadId: string,
  ): Promise<ThreadTreeDto | null> {
    const cacheKey = `${this.CACHE_PREFIX}${threadId}`;

    try {
      // Try to get from cache first
      const cached = await this.redis.getClient().get(cacheKey);
      if (cached && typeof cached === 'string') {
        const threadTree = JSON.parse(cached) as ThreadTreeDto;
        // Verify the cached thread belongs to the user
        if (threadTree.userId === userId) {
          this.logger.debug(`Cache hit for thread ${threadId}`);
          return threadTree;
        }
      }
    } catch (error) {
      this.logger.warn(`Cache read failed for thread ${threadId}:`, error);
    }

    // Cache miss - fetch from database
    this.logger.debug(`Cache miss for thread ${threadId}, fetching from DB`);
    const threadTree = await this.buildThreadTreeFromDB(userId, threadId);

    if (threadTree) {
      // Cache the result
      try {
        await this.redis
          .getClient()
          .setEx(cacheKey, this.CACHE_TTL, JSON.stringify(threadTree));
        this.logger.debug(`Cached thread tree for ${threadId}`);
      } catch (error) {
        this.logger.warn(`Cache write failed for thread ${threadId}:`, error);
      }
    }

    return threadTree;
  }

  /**
   * Build the complete thread tree from database using optimized queries
   */
  private async buildThreadTreeFromDB(
    userId: string,
    threadId: string,
  ): Promise<ThreadTreeDto | null> {
    // First, get the thread metadata
    const thread = await this.prisma.thread.findFirst({
      where: { id: threadId, userId },
    });

    if (!thread) {
      return null;
    }

    // Get all messages for this thread in a single query (including deleted ones for navigation)
    const messages = await this.prisma.message.findMany({
      where: {
        threadId,
      },
      orderBy: [{ createdAt: 'asc' }, { siblingPosition: 'asc' }],
    });

    if (!messages.length) {
      return {
        id: thread.id,
        title: thread.title,
        userId: thread.userId,
        createdAt: thread.createdAt.toISOString(),
        updatedAt: thread.updatedAt.toISOString(),
        messages: [],
        currentPath: [],
      };
    }

    // Build the tree structure
    const messageMap = new Map<string, ThreadMessageDto>();
    const rootMessages: ThreadMessageDto[] = [];

    // First pass: create all message DTOs
    for (const msg of messages) {
      const dto: ThreadMessageDto = {
        id: msg.id,
        content: msg.content,
        role: msg.role as 'USER' | 'ASSISTANT' | 'SYSTEM',
        createdAt: msg.createdAt.toISOString(),
        model: msg.model || undefined,
        threadId: msg.threadId,
        parentId: msg.parentId,
        nextMessageId: msg.nextMessageId,
        siblingPosition: msg.siblingPosition,
        deletedAt: msg.deletedAt?.toISOString() || undefined,
        children: [],
        siblingCount: 0, // Will be calculated
      };
      messageMap.set(msg.id, dto);

      if (!msg.parentId) {
        rootMessages.push(dto);
      }
    }

    // Second pass: build parent-child relationships and calculate sibling counts
    for (const msg of messages) {
      const dto = messageMap.get(msg.id)!;

      if (msg.parentId) {
        const parent = messageMap.get(msg.parentId);
        if (parent) {
          parent.children.push(dto);
        }
      }
    }

    // Third pass: calculate sibling counts
    for (const dto of messageMap.values()) {
      if (dto.parentId) {
        const parent = messageMap.get(dto.parentId);
        if (parent) {
          dto.siblingCount = parent.children.length;
        }
      } else {
        dto.siblingCount = rootMessages.length;
      }
    }

    // Build the current active path
    const currentPath = this.buildCurrentPath(rootMessages, messageMap);

    return {
      id: thread.id,
      title: thread.title,
      userId: thread.userId,
      createdAt: thread.createdAt.toISOString(),
      updatedAt: thread.updatedAt.toISOString(),
      messages: Array.from(messageMap.values()),
      currentPath,
    };
  }

  /**
   * Build the current active conversation path
   */
  private buildCurrentPath(
    rootMessages: ThreadMessageDto[],
    messageMap: Map<string, ThreadMessageDto>,
  ): string[] {
    if (!rootMessages.length) return [];

    const path: string[] = [];
    let current: ThreadMessageDto | undefined = rootMessages[0]; // Start with first root message

    // If root is SYSTEM container, follow its nextMessageId to find the real first message
    if (current.role === 'SYSTEM' && current.nextMessageId) {
      const next = messageMap.get(current.nextMessageId);
      if (next) {
        current = next;
      }
    }

    // Follow the active path. The path represents the current state of the
    // conversation branch, including any deleted messages within it. The client
    // is responsible for rendering them appropriately.
    while (current) {
      path.push(current.id); // ALWAYS add the message to the path.

      if (!current.nextMessageId) break;

      const next = messageMap.get(current.nextMessageId);
      if (!next) break;

      current = next;
    }

    return path;
  }

  /**
   * Invalidate cache for a specific thread
   */
  async invalidateThreadCache(threadId: string): Promise<void> {
    const cacheKey = `${this.CACHE_PREFIX}${threadId}`;
    try {
      await this.redis.getClient().del(cacheKey);
      this.logger.debug(`Invalidated cache for thread ${threadId}`);
    } catch (error) {
      this.logger.warn(
        `Cache invalidation failed for thread ${threadId}:`,
        error,
      );
    }
  }

  /**
   * Invalidate cache for all threads of a user
   */
  async invalidateUserThreadsCache(userId: string): Promise<void> {
    try {
      const pattern = `${this.CACHE_PREFIX}*`;
      const keys = await this.redis.getClient().keys(pattern);

      if (keys.length > 0) {
        // Get all cached threads and filter by userId
        const pipeline = this.redis.getClient().multi();
        keys.forEach((key) => pipeline.get(key));
        const results = await pipeline.exec();

        const keysToDelete: string[] = [];
        results?.forEach((result, index) => {
          if (result && result[1]) {
            try {
              const threadTree = JSON.parse(
                result[1] as string,
              ) as ThreadTreeDto;
              if (threadTree.userId === userId) {
                keysToDelete.push(keys[index]);
              }
            } catch {
              // Invalid JSON, delete anyway
              keysToDelete.push(keys[index]);
            }
          }
        });

        if (keysToDelete.length > 0) {
          await this.redis.getClient().del(keysToDelete);
          this.logger.debug(
            `Invalidated ${keysToDelete.length} cached threads for user ${userId}`,
          );
        }
      }
    } catch (error) {
      this.logger.warn(
        `User cache invalidation failed for user ${userId}:`,
        error,
      );
    }
  }

  /**
   * Update the current path in cache after navigation
   */
  async updateCurrentPath(threadId: string, newPath: string[]): Promise<void> {
    const cacheKey = `${this.CACHE_PREFIX}${threadId}`;

    try {
      const cached = await this.redis.getClient().get(cacheKey);
      if (cached && typeof cached === 'string') {
        const threadTree = JSON.parse(cached) as ThreadTreeDto;
        threadTree.currentPath = newPath;

        await this.redis
          .getClient()
          .setEx(cacheKey, this.CACHE_TTL, JSON.stringify(threadTree));

        this.logger.debug(`Updated current path for thread ${threadId}`);
      }
    } catch (error) {
      this.logger.warn(
        `Failed to update current path for thread ${threadId}:`,
        error,
      );
    }
  }
}
