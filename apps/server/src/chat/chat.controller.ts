// apps/server/src/chat/chat.controller.ts
import {
  Controller,
  Post,
  Body,
  UseGuards,
  Req,
  Sse,
  Param,
  Get,
  Logger,
  Put,
  Delete,
  HttpCode,
  HttpStatus,
  NotFoundException,
} from '@nestjs/common';
import { Request } from 'express';
import { Observable } from 'rxjs';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ChatService } from './services/chat.service';
import {
  CreateChatMessageDto,
  ChatMessageResponseDto,
  EditChatMessageDto,
  NavigateSiblingDto,
  CreateThreadDto,
  ThreadTreeDto,
} from './dto/chat-message.dto';

@Controller('api/chat')
@UseGuards(JwtAuthGuard)
export class ChatController {
  private readonly logger = new Logger(ChatController.name);

  constructor(private chatService: ChatService) {}

  @Post('message')
  async createMessage(
    @Body() createChatDto: CreateChatMessageDto,
    @Req() req: Request,
  ): Promise<{ status: string; data: ChatMessageResponseDto }> {
    const userId = (req.user as { id: string }).id;

    // Pass the whole DTO to the service
    const message = await this.chatService.createMessage(userId, createChatDto);

    return {
      status: 'success',
      data: message,
    };
  }

  @Put('message/:id')
  async editMessage(
    @Param('id') messageId: string,
    @Body() editChatDto: EditChatMessageDto,
    @Req() req: Request,
  ): Promise<{ status: string; data: ThreadTreeDto }> {
    const userId = (req.user as { id: string }).id;
    const newTree = await this.chatService.editMessage(
      userId,
      messageId,
      editChatDto.content,
    );
    return { status: 'success', data: newTree };
  }

  @Delete('message/:id')
  @HttpCode(HttpStatus.OK)
  async deleteMessage(
    @Param('id') messageId: string,
    @Req() req: Request,
  ): Promise<{ status: string; data: ThreadTreeDto }> {
    const userId = (req.user as { id: string }).id;
    const newTree = await this.chatService.deleteMessage(userId, messageId);
    return { status: 'success', data: newTree };
  }

  private createStreamObservable(
    userId: string,
    userMessageId: string,
    model: string,
  ): Observable<MessageEvent> {
    return new Observable((observer) => {
      const generate = async () => {
        try {
          const streamStartTime = Date.now();
          let totalChars = 0;
          const stream = this.chatService.generateAIResponse(
            userId,
            userMessageId,
            model,
          );

          for await (const event of stream) {
            if (event.type === 'final') {
              observer.next({
                data: JSON.stringify({
                  finalTree: event.payload,
                  isComplete: true,
                }),
              } as MessageEvent);
            } else if (event.type === 'chunk') {
              totalChars += event.payload.length;
              const elapsedSeconds = (Date.now() - streamStartTime) / 1000;
              const cps = elapsedSeconds > 0 ? totalChars / elapsedSeconds : 0;
              observer.next({
                data: JSON.stringify({
                  content: event.payload,
                  isComplete: false,
                  model: model,
                  debug: { serverTimestamp: Date.now(), tps: cps },
                }),
              } as MessageEvent);
            }
          }
          observer.complete();
        } catch (error) {
          this.logger.error(
            `Error during stream: ${error.message}`,
            (error as Error).stack,
          );
          observer.error(error);
        }
      };
      generate();
    });
  }

  @Sse('stream/:userMessageId/regenerate')
  streamRegenerateResponse(
    @Param('userMessageId') userMessageId: string,
    @Req() req: Request,
  ): Observable<MessageEvent> {
    const userId = (req.user as { id: string }).id;
    const model = 'gemini-2.5-flash-lite';
    return this.createStreamObservable(userId, userMessageId, model);
  }

  @Sse('stream/:userMessageId')
  streamResponse(
    @Param('userMessageId') userMessageId: string,
    @Req() req: Request,
  ): Observable<MessageEvent> {
    const userId = (req.user as { id: string }).id;
    const aiModel = 'gemini-2.5-flash-lite';
    return this.createStreamObservable(userId, userMessageId, aiModel);
  }

  @Post('navigate')
  @HttpCode(HttpStatus.OK)
  async navigate(
    @Body() navigateSiblingDto: NavigateSiblingDto,
    @Req() req: Request,
  ): Promise<{ status: string }> {
    const userId = (req.user as { id: string }).id;
    await this.chatService.persistNavigation(
      userId,
      navigateSiblingDto.messageId,
      navigateSiblingDto.direction,
    );
    return { status: 'success' };
  }

  @Get('history/:threadId')
  async getHistory(
    @Param('threadId') threadId: string,
    @Req() req: Request,
  ): Promise<{ status: string; data: ChatMessageResponseDto[] }> {
    const userId = (req.user as { id: string }).id;
    const messages = await this.chatService.getConversationHistoryOptimized(
      userId,
      threadId,
    );
    return {
      status: 'success',
      data: messages,
    };
  }

  @Get('thread-tree/:threadId')
  async getThreadTree(
    @Param('threadId') threadId: string,
    @Req() req: Request,
  ): Promise<{ status: string; data: any }> {
    const userId = (req.user as { id: string }).id;
    const threadTree = await this.chatService.getThreadTree(userId, threadId);
    if (!threadTree) {
      throw new NotFoundException('Thread not found');
    }
    return { status: 'success', data: threadTree };
  }

  @Get('threads')
  async getThreads(@Req() req: Request) {
    const userId = (req.user as { id: string }).id;
    const threads = await this.chatService.getUserThreads(userId);
    return {
      status: 'success',
      data: threads,
    };
  }

  @Post('threads')
  @UseGuards(JwtAuthGuard)
  async createThread(
    @Body() createThreadDto: CreateThreadDto,
    @Req() req: Request,
  ) {
    const userId = (req.user as { id: string }).id;
    const thread = await this.chatService.createThread(
      userId,
      createThreadDto.title,
    );
    return { status: 'success', data: thread };
  }
}
