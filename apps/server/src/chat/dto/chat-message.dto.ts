import { IsString, IsNotEmpty, IsOptional } from 'class-validator';

export class CreateChatMessageDto {
  @IsString()
  @IsNotEmpty()
  content: string;

  @IsString()
  @IsNotEmpty()
  model: string;

  @IsString()
  @IsOptional()
  threadId?: string;

  @IsString()
  @IsOptional()
  parentId?: string; // ID of the previous message

  @IsString()
  @IsOptional()
  idempotencyKey?: string;
}

export class EditChatMessageDto {
  @IsString()
  @IsNotEmpty()
  content: string;
}

export class NavigateSiblingDto {
  @IsString()
  @IsNotEmpty()
  messageId: string;

  @IsString()
  @IsNotEmpty()
  direction: 'next' | 'prev';
}

export class ChatMessageResponseDto {
  id: string;
  content: string;
  role: 'USER' | 'ASSISTANT' | 'SYSTEM';
  createdAt: string;
  model?: string;
  threadId?: string;
  parentId?: string | null;

  // Tree navigation properties
  siblingCount: number;
  siblingPosition: number;
  deletedAt?: string | null;
}

export class CreateThreadDto {
  @IsString()
  @IsNotEmpty()
  title: string;
}

// Enhanced DTOs for optimized navigation
export class ThreadTreeDto {
  id: string;
  title: string;
  userId: string;
  createdAt: string;
  updatedAt: string;
  messages: ThreadMessageDto[];
  currentPath: string[]; // Array of message IDs representing the current active path
}

export class ThreadMessageDto {
  id: string;
  content: string;
  role: 'USER' | 'ASSISTANT' | 'SYSTEM';
  createdAt: string;
  model?: string;
  threadId: string;
  parentId?: string | null;
  nextMessageId?: string | null;
  siblingPosition: number;
  deletedAt?: string | null;

  // Navigation metadata
  children: ThreadMessageDto[];
  siblingCount: number;
}
